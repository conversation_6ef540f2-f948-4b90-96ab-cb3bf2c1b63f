import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import { apiService } from '../../services/api';

// Dashboard metrics interfaces
interface FleetMetrics {
  totalVehicles: number;
  activeVehicles: number;
  inactiveVehicles: number;
  maintenanceVehicles: number;
  totalDrivers: number;
  activeDrivers: number;
  unassignedDrivers: number;
}

interface AlertMetrics {
  totalAlerts: number;
  criticalAlerts: number;
  highAlerts: number;
  mediumAlerts: number;
  lowAlerts: number;
  acknowledgedAlerts: number;
  unacknowledgedAlerts: number;
}

interface TelemetryMetrics {
  averageSpeed: number;
  totalDistance: number;
  averageFuelLevel: number;
  activeVehiclesCount: number;
  lastUpdateTime: string;
}

interface PerformanceMetrics {
  fuelEfficiency: number;
  maintenanceCosts: number;
  driverPerformance: number;
  vehicleUtilization: number;
  period: 'day' | 'week' | 'month';
}

interface RecentActivity {
  id: string;
  type: 'alert' | 'maintenance' | 'driver_assignment' | 'vehicle_status';
  message: string;
  timestamp: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  vehicleId?: string;
  driverId?: string;
}

interface DashboardSummary {
  fleetMetrics: FleetMetrics;
  alertMetrics: AlertMetrics;
  telemetryMetrics: TelemetryMetrics;
  performanceMetrics: PerformanceMetrics;
  recentActivity: RecentActivity[];
  lastUpdated: string;
}

// Dashboard state interface
interface DashboardState {
  summary: DashboardSummary | null;
  fleetMetrics: FleetMetrics | null;
  alertMetrics: AlertMetrics | null;
  telemetryMetrics: TelemetryMetrics | null;
  performanceMetrics: PerformanceMetrics | null;
  recentActivity: RecentActivity[];
  isLoading: boolean;
  isLoadingMetrics: boolean;
  isLoadingActivity: boolean;
  error: string | null;
  selectedPeriod: 'day' | 'week' | 'month';
  autoRefresh: boolean;
  refreshInterval: number; // in seconds
  lastUpdated: string | null;
}

const initialState: DashboardState = {
  summary: null,
  fleetMetrics: null,
  alertMetrics: null,
  telemetryMetrics: null,
  performanceMetrics: null,
  recentActivity: [],
  isLoading: false,
  isLoadingMetrics: false,
  isLoadingActivity: false,
  error: null,
  selectedPeriod: 'day',
  autoRefresh: true,
  refreshInterval: 30,
  lastUpdated: null,
};

// Async thunks
export const fetchDashboardSummary = createAsyncThunk(
  'dashboard/fetchDashboardSummary',
  async (period: 'day' | 'week' | 'month' = 'day', { rejectWithValue }) => {
    try {
      const response = await apiService.get<DashboardSummary>(`/dashboard/summary?period=${period}`);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch dashboard summary';
      return rejectWithValue(message);
    }
  }
);

export const fetchFleetMetrics = createAsyncThunk(
  'dashboard/fetchFleetMetrics',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.get<FleetMetrics>('/dashboard/fleet-metrics');
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch fleet metrics';
      return rejectWithValue(message);
    }
  }
);

export const fetchAlertMetrics = createAsyncThunk(
  'dashboard/fetchAlertMetrics',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.get<AlertMetrics>('/dashboard/alert-metrics');
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch alert metrics';
      return rejectWithValue(message);
    }
  }
);

export const fetchTelemetryMetrics = createAsyncThunk(
  'dashboard/fetchTelemetryMetrics',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.get<TelemetryMetrics>('/dashboard/telemetry-metrics');
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch telemetry metrics';
      return rejectWithValue(message);
    }
  }
);

export const fetchPerformanceMetrics = createAsyncThunk(
  'dashboard/fetchPerformanceMetrics',
  async (period: 'day' | 'week' | 'month' = 'day', { rejectWithValue }) => {
    try {
      const response = await apiService.get<PerformanceMetrics>(`/dashboard/performance-metrics?period=${period}`);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch performance metrics';
      return rejectWithValue(message);
    }
  }
);

export const fetchRecentActivity = createAsyncThunk(
  'dashboard/fetchRecentActivity',
  async (limit: number = 10, { rejectWithValue }) => {
    try {
      const response = await apiService.get<RecentActivity[]>(`/dashboard/recent-activity?limit=${limit}`);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch recent activity';
      return rejectWithValue(message);
    }
  }
);

export const refreshDashboard = createAsyncThunk(
  'dashboard/refreshDashboard',
  async (period: 'day' | 'week' | 'month' = 'day', { dispatch }) => {
    // Fetch all dashboard data concurrently
    const promises = [
      dispatch(fetchFleetMetrics()),
      dispatch(fetchAlertMetrics()),
      dispatch(fetchTelemetryMetrics()),
      dispatch(fetchPerformanceMetrics(period)),
      dispatch(fetchRecentActivity()),
    ];
    
    await Promise.all(promises);
    return period;
  }
);

const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    clearError: state => {
      state.error = null;
    },
    setSelectedPeriod: (state, action: PayloadAction<'day' | 'week' | 'month'>) => {
      state.selectedPeriod = action.payload;
    },
    setAutoRefresh: (state, action: PayloadAction<boolean>) => {
      state.autoRefresh = action.payload;
    },
    setRefreshInterval: (state, action: PayloadAction<number>) => {
      state.refreshInterval = action.payload;
    },
    updateMetrics: (state, action: PayloadAction<Partial<DashboardSummary>>) => {
      if (state.summary) {
        state.summary = { ...state.summary, ...action.payload };
      }
      state.lastUpdated = new Date().toISOString();
    },
    addRecentActivity: (state, action: PayloadAction<RecentActivity>) => {
      state.recentActivity.unshift(action.payload);
      // Keep only the latest 20 activities
      if (state.recentActivity.length > 20) {
        state.recentActivity = state.recentActivity.slice(0, 20);
      }
    },
    clearDashboard: state => {
      state.summary = null;
      state.fleetMetrics = null;
      state.alertMetrics = null;
      state.telemetryMetrics = null;
      state.performanceMetrics = null;
      state.recentActivity = [];
      state.lastUpdated = null;
    },
  },
  extraReducers: builder => {
    builder
      // Fetch dashboard summary cases
      .addCase(fetchDashboardSummary.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchDashboardSummary.fulfilled, (state, action) => {
        state.isLoading = false;
        state.summary = action.payload;
        state.fleetMetrics = action.payload.fleetMetrics;
        state.alertMetrics = action.payload.alertMetrics;
        state.telemetryMetrics = action.payload.telemetryMetrics;
        state.performanceMetrics = action.payload.performanceMetrics;
        state.recentActivity = action.payload.recentActivity;
        state.lastUpdated = action.payload.lastUpdated;
        state.error = null;
      })
      .addCase(fetchDashboardSummary.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch fleet metrics cases
      .addCase(fetchFleetMetrics.pending, state => {
        state.isLoadingMetrics = true;
        state.error = null;
      })
      .addCase(fetchFleetMetrics.fulfilled, (state, action) => {
        state.isLoadingMetrics = false;
        state.fleetMetrics = action.payload;
        state.lastUpdated = new Date().toISOString();
        state.error = null;
      })
      .addCase(fetchFleetMetrics.rejected, (state, action) => {
        state.isLoadingMetrics = false;
        state.error = action.payload as string;
      })
      // Fetch alert metrics cases
      .addCase(fetchAlertMetrics.pending, state => {
        state.isLoadingMetrics = true;
        state.error = null;
      })
      .addCase(fetchAlertMetrics.fulfilled, (state, action) => {
        state.isLoadingMetrics = false;
        state.alertMetrics = action.payload;
        state.lastUpdated = new Date().toISOString();
        state.error = null;
      })
      .addCase(fetchAlertMetrics.rejected, (state, action) => {
        state.isLoadingMetrics = false;
        state.error = action.payload as string;
      })
      // Fetch telemetry metrics cases
      .addCase(fetchTelemetryMetrics.pending, state => {
        state.isLoadingMetrics = true;
        state.error = null;
      })
      .addCase(fetchTelemetryMetrics.fulfilled, (state, action) => {
        state.isLoadingMetrics = false;
        state.telemetryMetrics = action.payload;
        state.lastUpdated = new Date().toISOString();
        state.error = null;
      })
      .addCase(fetchTelemetryMetrics.rejected, (state, action) => {
        state.isLoadingMetrics = false;
        state.error = action.payload as string;
      })
      // Fetch performance metrics cases
      .addCase(fetchPerformanceMetrics.pending, state => {
        state.isLoadingMetrics = true;
        state.error = null;
      })
      .addCase(fetchPerformanceMetrics.fulfilled, (state, action) => {
        state.isLoadingMetrics = false;
        state.performanceMetrics = action.payload;
        state.lastUpdated = new Date().toISOString();
        state.error = null;
      })
      .addCase(fetchPerformanceMetrics.rejected, (state, action) => {
        state.isLoadingMetrics = false;
        state.error = action.payload as string;
      })
      // Fetch recent activity cases
      .addCase(fetchRecentActivity.pending, state => {
        state.isLoadingActivity = true;
        state.error = null;
      })
      .addCase(fetchRecentActivity.fulfilled, (state, action) => {
        state.isLoadingActivity = false;
        state.recentActivity = action.payload;
        state.lastUpdated = new Date().toISOString();
        state.error = null;
      })
      .addCase(fetchRecentActivity.rejected, (state, action) => {
        state.isLoadingActivity = false;
        state.error = action.payload as string;
      })
      // Refresh dashboard cases
      .addCase(refreshDashboard.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(refreshDashboard.fulfilled, (state, action) => {
        state.isLoading = false;
        state.selectedPeriod = action.payload;
        state.lastUpdated = new Date().toISOString();
        state.error = null;
      })
      .addCase(refreshDashboard.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setSelectedPeriod,
  setAutoRefresh,
  setRefreshInterval,
  updateMetrics,
  addRecentActivity,
  clearDashboard,
} = dashboardSlice.actions;

// Selectors
export const selectDashboardSummary = (state: { dashboard: DashboardState }) => state.dashboard.summary;
export const selectFleetMetrics = (state: { dashboard: DashboardState }) => state.dashboard.fleetMetrics;
export const selectAlertMetrics = (state: { dashboard: DashboardState }) => state.dashboard.alertMetrics;
export const selectTelemetryMetrics = (state: { dashboard: DashboardState }) => state.dashboard.telemetryMetrics;
export const selectPerformanceMetrics = (state: { dashboard: DashboardState }) => state.dashboard.performanceMetrics;
export const selectRecentActivity = (state: { dashboard: DashboardState }) => state.dashboard.recentActivity;
export const selectDashboardLoading = (state: { dashboard: DashboardState }) => state.dashboard.isLoading;
export const selectDashboardMetricsLoading = (state: { dashboard: DashboardState }) => state.dashboard.isLoadingMetrics;
export const selectDashboardActivityLoading = (state: { dashboard: DashboardState }) => state.dashboard.isLoadingActivity;
export const selectDashboardError = (state: { dashboard: DashboardState }) => state.dashboard.error;
export const selectSelectedPeriod = (state: { dashboard: DashboardState }) => state.dashboard.selectedPeriod;
export const selectAutoRefresh = (state: { dashboard: DashboardState }) => state.dashboard.autoRefresh;
export const selectRefreshInterval = (state: { dashboard: DashboardState }) => state.dashboard.refreshInterval;
export const selectDashboardLastUpdated = (state: { dashboard: DashboardState }) => state.dashboard.lastUpdated;

export default dashboardSlice.reducer;
