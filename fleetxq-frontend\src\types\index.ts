// Common types for FleetXQ application

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'manager' | 'driver';
  isAuthenticated: boolean;
}

export interface Vehicle {
  id: string;
  make: string;
  model: string;
  year: number;
  licensePlate: string;
  vin: string;
  status: 'active' | 'maintenance' | 'inactive';
  driverId?: string;
}

export interface Driver {
  id: string;
  name: string;
  email: string;
  phone: string;
  licenseNumber: string;
  status: 'active' | 'inactive';
  vehicleId?: string;
}

export interface TelemetryData {
  vehicleId: string;
  timestamp: string;
  location: {
    latitude: number;
    longitude: number;
  };
  speed: number;
  fuelLevel: number;
  engineStatus: 'on' | 'off';
  temperature: number;
}

export interface Alert {
  id: string;
  vehicleId: string;
  type: 'maintenance' | 'speed' | 'fuel' | 'location' | 'engine';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: string;
  acknowledged: boolean;
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// Redux State Types
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

export interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

// Auth Types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

// Vehicle Types
export interface VehicleFormData {
  make: string;
  model: string;
  year: number;
  licensePlate: string;
  vin: string;
  status: 'active' | 'maintenance' | 'inactive';
  driverId?: string;
}

export interface VehicleFilters {
  status?: 'active' | 'maintenance' | 'inactive';
  search?: string;
}

// Driver Types
export interface DriverFormData {
  name: string;
  email: string;
  phone: string;
  licenseNumber: string;
  status: 'active' | 'inactive';
  vehicleId?: string;
}

export interface DriverFilters {
  status?: 'active' | 'inactive';
  search?: string;
  vehicleId?: string;
}

export interface DriverAssignment {
  driverId: string;
  vehicleId: string;
}

// Telemetry Types
export interface TelemetryQueryParams {
  vehicleId?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
}

export interface TelemetryFilters {
  vehicleId?: string;
  startDate?: string;
  endDate?: string;
}

export interface TelemetryUpdate {
  vehicleId: string;
  data: TelemetryData;
}

// Alert Types
export interface AlertQueryParams {
  vehicleId?: string;
  type?: 'maintenance' | 'speed' | 'fuel' | 'location' | 'engine';
  severity?: 'low' | 'medium' | 'high' | 'critical';
  acknowledged?: boolean;
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
}

export interface AlertFilters {
  vehicleId?: string;
  type?: 'maintenance' | 'speed' | 'fuel' | 'location' | 'engine';
  severity?: 'low' | 'medium' | 'high' | 'critical';
  acknowledged?: boolean;
  startDate?: string;
  endDate?: string;
}

export interface AlertAcknowledgment {
  alertId: string;
  userId: string;
  notes?: string;
}

export interface AlertResolution {
  alertId: string;
  userId: string;
  resolution: string;
  notes?: string;
}

export interface NotificationSettings {
  sound: boolean;
  desktop: boolean;
  email: boolean;
}

// Dashboard Types
export interface FleetMetrics {
  totalVehicles: number;
  activeVehicles: number;
  inactiveVehicles: number;
  maintenanceVehicles: number;
  totalDrivers: number;
  activeDrivers: number;
  unassignedDrivers: number;
}

export interface AlertMetrics {
  totalAlerts: number;
  criticalAlerts: number;
  highAlerts: number;
  mediumAlerts: number;
  lowAlerts: number;
  acknowledgedAlerts: number;
  unacknowledgedAlerts: number;
}

export interface TelemetryMetrics {
  averageSpeed: number;
  totalDistance: number;
  averageFuelLevel: number;
  activeVehiclesCount: number;
  lastUpdateTime: string;
}

export interface PerformanceMetrics {
  fuelEfficiency: number;
  maintenanceCosts: number;
  driverPerformance: number;
  vehicleUtilization: number;
  period: 'day' | 'week' | 'month';
}

export interface RecentActivity {
  id: string;
  type: 'alert' | 'maintenance' | 'driver_assignment' | 'vehicle_status';
  message: string;
  timestamp: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  vehicleId?: string;
  driverId?: string;
}

export interface DashboardSummary {
  fleetMetrics: FleetMetrics;
  alertMetrics: AlertMetrics;
  telemetryMetrics: TelemetryMetrics;
  performanceMetrics: PerformanceMetrics;
  recentActivity: RecentActivity[];
  lastUpdated: string;
}

// Async Thunk Types
export interface AsyncThunkConfig {
  rejectValue: string;
}

// Common Action Payload Types
export interface ErrorPayload {
  message: string;
  code?: string;
  details?: any;
}

export interface SuccessPayload<T = any> {
  data: T;
  message?: string;
}

// Real-time Connection Types
export interface ConnectionStatus {
  isConnected: boolean;
  lastConnected?: string;
  reconnectAttempts?: number;
}

// Form Validation Types
export interface ValidationError {
  field: string;
  message: string;
}

export interface FormState<T> {
  data: T;
  errors: ValidationError[];
  isValid: boolean;
  isDirty: boolean;
  isSubmitting: boolean;
}
