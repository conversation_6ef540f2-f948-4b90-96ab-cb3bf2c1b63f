import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { Alert, PaginatedResponse } from '../../types';
import { apiService } from '../../services/api';

// Alert query parameters interface
interface AlertQueryParams {
  vehicleId?: string;
  type?: 'maintenance' | 'speed' | 'fuel' | 'location' | 'engine';
  severity?: 'low' | 'medium' | 'high' | 'critical';
  acknowledged?: boolean;
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
}

// Alert acknowledgment interface
interface AlertAcknowledgment {
  alertId: string;
  userId: string;
  notes?: string;
}

// Alert resolution interface
interface AlertResolution {
  alertId: string;
  userId: string;
  resolution: string;
  notes?: string;
}

// Alerts state interface
interface AlertsState {
  // All alerts
  alerts: Alert[];
  // Active (unacknowledged) alerts
  activeAlerts: Alert[];
  // Critical alerts for immediate attention
  criticalAlerts: Alert[];
  // Selected alert for detailed view
  currentAlert: Alert | null;
  // Loading states
  isLoading: boolean;
  isAcknowledging: boolean;
  isResolving: boolean;
  isCreating: boolean;
  // Error state
  error: string | null;
  // Pagination
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  // Filters
  filters: {
    vehicleId?: string;
    type?: 'maintenance' | 'speed' | 'fuel' | 'location' | 'engine';
    severity?: 'low' | 'medium' | 'high' | 'critical';
    acknowledged?: boolean;
    startDate?: string;
    endDate?: string;
  };
  // Real-time updates
  isConnected: boolean;
  lastUpdated: string | null;
  // Notification settings
  notifications: {
    sound: boolean;
    desktop: boolean;
    email: boolean;
  };
}

const initialState: AlertsState = {
  alerts: [],
  activeAlerts: [],
  criticalAlerts: [],
  currentAlert: null,
  isLoading: false,
  isAcknowledging: false,
  isResolving: false,
  isCreating: false,
  error: null,
  pagination: {
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
  },
  filters: {},
  isConnected: false,
  lastUpdated: null,
  notifications: {
    sound: true,
    desktop: true,
    email: false,
  },
};

// Async thunks
export const fetchAlerts = createAsyncThunk(
  'alerts/fetchAlerts',
  async (params: AlertQueryParams = {}, { rejectWithValue }) => {
    try {
      const {
        vehicleId,
        type,
        severity,
        acknowledged,
        startDate,
        endDate,
        page = 1,
        pageSize = 20,
      } = params;
      
      let url = `/alerts?page=${page}&pageSize=${pageSize}`;
      
      if (vehicleId) url += `&vehicleId=${vehicleId}`;
      if (type) url += `&type=${type}`;
      if (severity) url += `&severity=${severity}`;
      if (acknowledged !== undefined) url += `&acknowledged=${acknowledged}`;
      if (startDate) url += `&startDate=${startDate}`;
      if (endDate) url += `&endDate=${endDate}`;
      
      const response = await apiService.get<PaginatedResponse<Alert>>(url);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch alerts';
      return rejectWithValue(message);
    }
  }
);

export const fetchActiveAlerts = createAsyncThunk(
  'alerts/fetchActiveAlerts',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.get<Alert[]>('/alerts/active');
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch active alerts';
      return rejectWithValue(message);
    }
  }
);

export const fetchCriticalAlerts = createAsyncThunk(
  'alerts/fetchCriticalAlerts',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.get<Alert[]>('/alerts/critical');
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch critical alerts';
      return rejectWithValue(message);
    }
  }
);

export const fetchAlertById = createAsyncThunk(
  'alerts/fetchAlertById',
  async (alertId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.get<Alert>(`/alerts/${alertId}`);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch alert';
      return rejectWithValue(message);
    }
  }
);

export const acknowledgeAlert = createAsyncThunk(
  'alerts/acknowledgeAlert',
  async ({ alertId, userId, notes }: AlertAcknowledgment, { rejectWithValue }) => {
    try {
      const response = await apiService.post<Alert>(`/alerts/${alertId}/acknowledge`, {
        userId,
        notes,
      });
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to acknowledge alert';
      return rejectWithValue(message);
    }
  }
);

export const resolveAlert = createAsyncThunk(
  'alerts/resolveAlert',
  async ({ alertId, userId, resolution, notes }: AlertResolution, { rejectWithValue }) => {
    try {
      const response = await apiService.post<Alert>(`/alerts/${alertId}/resolve`, {
        userId,
        resolution,
        notes,
      });
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to resolve alert';
      return rejectWithValue(message);
    }
  }
);

export const createAlert = createAsyncThunk(
  'alerts/createAlert',
  async (alertData: Omit<Alert, 'id' | 'timestamp' | 'acknowledged'>, { rejectWithValue }) => {
    try {
      const response = await apiService.post<Alert>('/alerts', alertData);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to create alert';
      return rejectWithValue(message);
    }
  }
);

const alertsSlice = createSlice({
  name: 'alerts',
  initialState,
  reducers: {
    clearError: state => {
      state.error = null;
    },
    setCurrentAlert: (state, action: PayloadAction<Alert | null>) => {
      state.currentAlert = action.payload;
    },
    setFilters: (state, action: PayloadAction<Partial<AlertsState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: state => {
      state.filters = {};
    },
    setPagination: (state, action: PayloadAction<Partial<AlertsState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    // Real-time alert updates
    addNewAlert: (state, action: PayloadAction<Alert>) => {
      const newAlert = action.payload;
      state.alerts.unshift(newAlert);
      
      if (!newAlert.acknowledged) {
        state.activeAlerts.unshift(newAlert);
      }
      
      if (newAlert.severity === 'critical') {
        state.criticalAlerts.unshift(newAlert);
      }
      
      state.pagination.total += 1;
      state.lastUpdated = new Date().toISOString();
    },
    updateAlert: (state, action: PayloadAction<Alert>) => {
      const updatedAlert = action.payload;
      
      // Update in all alerts
      const alertIndex = state.alerts.findIndex(a => a.id === updatedAlert.id);
      if (alertIndex !== -1) {
        state.alerts[alertIndex] = updatedAlert;
      }
      
      // Update in active alerts
      const activeIndex = state.activeAlerts.findIndex(a => a.id === updatedAlert.id);
      if (updatedAlert.acknowledged && activeIndex !== -1) {
        state.activeAlerts.splice(activeIndex, 1);
      } else if (!updatedAlert.acknowledged && activeIndex === -1) {
        state.activeAlerts.unshift(updatedAlert);
      } else if (activeIndex !== -1) {
        state.activeAlerts[activeIndex] = updatedAlert;
      }
      
      // Update in critical alerts
      const criticalIndex = state.criticalAlerts.findIndex(a => a.id === updatedAlert.id);
      if (updatedAlert.severity !== 'critical' && criticalIndex !== -1) {
        state.criticalAlerts.splice(criticalIndex, 1);
      } else if (updatedAlert.severity === 'critical' && criticalIndex === -1) {
        state.criticalAlerts.unshift(updatedAlert);
      } else if (criticalIndex !== -1) {
        state.criticalAlerts[criticalIndex] = updatedAlert;
      }
      
      // Update current alert if it's the same
      if (state.currentAlert?.id === updatedAlert.id) {
        state.currentAlert = updatedAlert;
      }
      
      state.lastUpdated = new Date().toISOString();
    },
    setConnectionStatus: (state, action: PayloadAction<boolean>) => {
      state.isConnected = action.payload;
    },
    updateNotificationSettings: (state, action: PayloadAction<Partial<AlertsState['notifications']>>) => {
      state.notifications = { ...state.notifications, ...action.payload };
    },
    clearAlerts: state => {
      state.alerts = [];
      state.activeAlerts = [];
      state.criticalAlerts = [];
      state.currentAlert = null;
      state.lastUpdated = null;
    },
  },
  extraReducers: builder => {
    builder
      // Fetch alerts cases
      .addCase(fetchAlerts.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAlerts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.alerts = action.payload.data;
        state.pagination = {
          page: action.payload.page,
          pageSize: action.payload.pageSize,
          total: action.payload.total,
          totalPages: action.payload.totalPages,
        };
        state.error = null;
      })
      .addCase(fetchAlerts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch active alerts cases
      .addCase(fetchActiveAlerts.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchActiveAlerts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.activeAlerts = action.payload;
        state.error = null;
      })
      .addCase(fetchActiveAlerts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch critical alerts cases
      .addCase(fetchCriticalAlerts.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCriticalAlerts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.criticalAlerts = action.payload;
        state.error = null;
      })
      .addCase(fetchCriticalAlerts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch alert by ID cases
      .addCase(fetchAlertById.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAlertById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentAlert = action.payload;
        state.error = null;
      })
      .addCase(fetchAlertById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Acknowledge alert cases
      .addCase(acknowledgeAlert.pending, state => {
        state.isAcknowledging = true;
        state.error = null;
      })
      .addCase(acknowledgeAlert.fulfilled, (state, action) => {
        state.isAcknowledging = false;
        
        // Update the alert in all relevant arrays
        const updatedAlert = action.payload;
        
        // Update in alerts array
        const alertIndex = state.alerts.findIndex(a => a.id === updatedAlert.id);
        if (alertIndex !== -1) {
          state.alerts[alertIndex] = updatedAlert;
        }
        
        // Remove from active alerts since it's now acknowledged
        state.activeAlerts = state.activeAlerts.filter(a => a.id !== updatedAlert.id);
        
        // Update current alert if it's the same
        if (state.currentAlert?.id === updatedAlert.id) {
          state.currentAlert = updatedAlert;
        }
        
        state.error = null;
      })
      .addCase(acknowledgeAlert.rejected, (state, action) => {
        state.isAcknowledging = false;
        state.error = action.payload as string;
      })
      // Resolve alert cases
      .addCase(resolveAlert.pending, state => {
        state.isResolving = true;
        state.error = null;
      })
      .addCase(resolveAlert.fulfilled, (state, action) => {
        state.isResolving = false;
        
        const resolvedAlert = action.payload;
        
        // Update in alerts array
        const alertIndex = state.alerts.findIndex(a => a.id === resolvedAlert.id);
        if (alertIndex !== -1) {
          state.alerts[alertIndex] = resolvedAlert;
        }
        
        // Remove from active and critical alerts
        state.activeAlerts = state.activeAlerts.filter(a => a.id !== resolvedAlert.id);
        state.criticalAlerts = state.criticalAlerts.filter(a => a.id !== resolvedAlert.id);
        
        // Update current alert if it's the same
        if (state.currentAlert?.id === resolvedAlert.id) {
          state.currentAlert = resolvedAlert;
        }
        
        state.error = null;
      })
      .addCase(resolveAlert.rejected, (state, action) => {
        state.isResolving = false;
        state.error = action.payload as string;
      })
      // Create alert cases
      .addCase(createAlert.pending, state => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(createAlert.fulfilled, (state, action) => {
        state.isCreating = false;
        const newAlert = action.payload;
        
        state.alerts.unshift(newAlert);
        state.activeAlerts.unshift(newAlert);
        
        if (newAlert.severity === 'critical') {
          state.criticalAlerts.unshift(newAlert);
        }
        
        state.pagination.total += 1;
        state.error = null;
      })
      .addCase(createAlert.rejected, (state, action) => {
        state.isCreating = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setCurrentAlert,
  setFilters,
  clearFilters,
  setPagination,
  addNewAlert,
  updateAlert,
  setConnectionStatus,
  updateNotificationSettings,
  clearAlerts,
} = alertsSlice.actions;

// Selectors
export const selectAlerts = (state: { alerts: AlertsState }) => state.alerts.alerts;
export const selectActiveAlerts = (state: { alerts: AlertsState }) => state.alerts.activeAlerts;
export const selectCriticalAlerts = (state: { alerts: AlertsState }) => state.alerts.criticalAlerts;
export const selectCurrentAlert = (state: { alerts: AlertsState }) => state.alerts.currentAlert;
export const selectAlertsLoading = (state: { alerts: AlertsState }) => state.alerts.isLoading;
export const selectAlertsAcknowledging = (state: { alerts: AlertsState }) => state.alerts.isAcknowledging;
export const selectAlertsResolving = (state: { alerts: AlertsState }) => state.alerts.isResolving;
export const selectAlertsCreating = (state: { alerts: AlertsState }) => state.alerts.isCreating;
export const selectAlertsError = (state: { alerts: AlertsState }) => state.alerts.error;
export const selectAlertsPagination = (state: { alerts: AlertsState }) => state.alerts.pagination;
export const selectAlertsFilters = (state: { alerts: AlertsState }) => state.alerts.filters;
export const selectAlertsConnectionStatus = (state: { alerts: AlertsState }) => state.alerts.isConnected;
export const selectAlertsLastUpdated = (state: { alerts: AlertsState }) => state.alerts.lastUpdated;
export const selectNotificationSettings = (state: { alerts: AlertsState }) => state.alerts.notifications;

export default alertsSlice.reducer;
