import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import type { TelemetryData, PaginatedResponse } from '../../types';
import { apiService } from '../../services/api';

// Telemetry query parameters interface
interface TelemetryQueryParams {
  vehicleId?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
}

// Real-time telemetry update interface
interface TelemetryUpdate {
  vehicleId: string;
  data: TelemetryData;
}

// Telemetry state interface
interface TelemetryState {
  // Current/latest telemetry data for all vehicles
  currentTelemetry: Record<string, TelemetryData>;
  // Historical telemetry data
  historicalData: TelemetryData[];
  // Selected vehicle for detailed view
  selectedVehicleId: string | null;
  // Loading states
  isLoading: boolean;
  isLoadingHistory: boolean;
  isLoadingCurrent: boolean;
  // Error state
  error: string | null;
  // Pagination for historical data
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  // Filters for historical data
  filters: {
    vehicleId?: string;
    startDate?: string;
    endDate?: string;
  };
  // Real-time connection status
  isConnected: boolean;
  lastUpdated: string | null;
}

const initialState: TelemetryState = {
  currentTelemetry: {},
  historicalData: [],
  selectedVehicleId: null,
  isLoading: false,
  isLoadingHistory: false,
  isLoadingCurrent: false,
  error: null,
  pagination: {
    page: 1,
    pageSize: 50,
    total: 0,
    totalPages: 0,
  },
  filters: {},
  isConnected: false,
  lastUpdated: null,
};

// Async thunks
export const fetchCurrentTelemetry = createAsyncThunk(
  'telemetry/fetchCurrentTelemetry',
  async (vehicleId?: string, { rejectWithValue }) => {
    try {
      const url = vehicleId ? `/telemetry/current/${vehicleId}` : '/telemetry/current';
      const response = await apiService.get<TelemetryData | TelemetryData[]>(url);
      
      if (vehicleId) {
        return { vehicleId, data: response.data as TelemetryData };
      } else {
        return { data: response.data as TelemetryData[] };
      }
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch current telemetry';
      return rejectWithValue(message);
    }
  }
);

export const fetchTelemetryHistory = createAsyncThunk(
  'telemetry/fetchTelemetryHistory',
  async (params: TelemetryQueryParams = {}, { rejectWithValue }) => {
    try {
      const { vehicleId, startDate, endDate, page = 1, pageSize = 50 } = params;
      let url = `/telemetry/history?page=${page}&pageSize=${pageSize}`;
      
      if (vehicleId) url += `&vehicleId=${vehicleId}`;
      if (startDate) url += `&startDate=${startDate}`;
      if (endDate) url += `&endDate=${endDate}`;
      
      const response = await apiService.get<PaginatedResponse<TelemetryData>>(url);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch telemetry history';
      return rejectWithValue(message);
    }
  }
);

export const fetchLatestTelemetryForVehicle = createAsyncThunk(
  'telemetry/fetchLatestTelemetryForVehicle',
  async (vehicleId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.get<TelemetryData>(`/telemetry/latest/${vehicleId}`);
      return { vehicleId, data: response.data };
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch latest telemetry';
      return rejectWithValue(message);
    }
  }
);

export const fetchTelemetryStats = createAsyncThunk(
  'telemetry/fetchTelemetryStats',
  async (params: { vehicleId?: string; period?: 'day' | 'week' | 'month' } = {}, { rejectWithValue }) => {
    try {
      const { vehicleId, period = 'day' } = params;
      let url = `/telemetry/stats?period=${period}`;
      
      if (vehicleId) url += `&vehicleId=${vehicleId}`;
      
      const response = await apiService.get<any>(url);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch telemetry stats';
      return rejectWithValue(message);
    }
  }
);

const telemetrySlice = createSlice({
  name: 'telemetry',
  initialState,
  reducers: {
    clearError: state => {
      state.error = null;
    },
    setSelectedVehicle: (state, action: PayloadAction<string | null>) => {
      state.selectedVehicleId = action.payload;
    },
    setFilters: (state, action: PayloadAction<Partial<TelemetryState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: state => {
      state.filters = {};
    },
    setPagination: (state, action: PayloadAction<Partial<TelemetryState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    // Real-time telemetry updates
    updateTelemetryData: (state, action: PayloadAction<TelemetryUpdate>) => {
      const { vehicleId, data } = action.payload;
      state.currentTelemetry[vehicleId] = data;
      state.lastUpdated = new Date().toISOString();
    },
    setConnectionStatus: (state, action: PayloadAction<boolean>) => {
      state.isConnected = action.payload;
    },
    clearTelemetryData: state => {
      state.currentTelemetry = {};
      state.historicalData = [];
      state.selectedVehicleId = null;
      state.lastUpdated = null;
    },
  },
  extraReducers: builder => {
    builder
      // Fetch current telemetry cases
      .addCase(fetchCurrentTelemetry.pending, state => {
        state.isLoadingCurrent = true;
        state.error = null;
      })
      .addCase(fetchCurrentTelemetry.fulfilled, (state, action) => {
        state.isLoadingCurrent = false;
        
        if ('vehicleId' in action.payload && action.payload.vehicleId) {
          // Single vehicle telemetry
          state.currentTelemetry[action.payload.vehicleId] = action.payload.data as TelemetryData;
        } else if ('data' in action.payload && Array.isArray(action.payload.data)) {
          // Multiple vehicles telemetry
          const telemetryArray = action.payload.data as TelemetryData[];
          telemetryArray.forEach(telemetry => {
            state.currentTelemetry[telemetry.vehicleId] = telemetry;
          });
        }
        
        state.lastUpdated = new Date().toISOString();
        state.error = null;
      })
      .addCase(fetchCurrentTelemetry.rejected, (state, action) => {
        state.isLoadingCurrent = false;
        state.error = action.payload as string;
      })
      // Fetch telemetry history cases
      .addCase(fetchTelemetryHistory.pending, state => {
        state.isLoadingHistory = true;
        state.error = null;
      })
      .addCase(fetchTelemetryHistory.fulfilled, (state, action) => {
        state.isLoadingHistory = false;
        state.historicalData = action.payload.data;
        state.pagination = {
          page: action.payload.page,
          pageSize: action.payload.pageSize,
          total: action.payload.total,
          totalPages: action.payload.totalPages,
        };
        state.error = null;
      })
      .addCase(fetchTelemetryHistory.rejected, (state, action) => {
        state.isLoadingHistory = false;
        state.error = action.payload as string;
      })
      // Fetch latest telemetry for vehicle cases
      .addCase(fetchLatestTelemetryForVehicle.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchLatestTelemetryForVehicle.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentTelemetry[action.payload.vehicleId] = action.payload.data;
        state.lastUpdated = new Date().toISOString();
        state.error = null;
      })
      .addCase(fetchLatestTelemetryForVehicle.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch telemetry stats cases
      .addCase(fetchTelemetryStats.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTelemetryStats.fulfilled, state => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(fetchTelemetryStats.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setSelectedVehicle,
  setFilters,
  clearFilters,
  setPagination,
  updateTelemetryData,
  setConnectionStatus,
  clearTelemetryData,
} = telemetrySlice.actions;

// Selectors
export const selectCurrentTelemetry = (state: { telemetry: TelemetryState }) => state.telemetry.currentTelemetry;
export const selectTelemetryForVehicle = (vehicleId: string) => (state: { telemetry: TelemetryState }) =>
  state.telemetry.currentTelemetry[vehicleId];
export const selectHistoricalTelemetry = (state: { telemetry: TelemetryState }) => state.telemetry.historicalData;
export const selectSelectedVehicleId = (state: { telemetry: TelemetryState }) => state.telemetry.selectedVehicleId;
export const selectTelemetryLoading = (state: { telemetry: TelemetryState }) => state.telemetry.isLoading;
export const selectTelemetryHistoryLoading = (state: { telemetry: TelemetryState }) => state.telemetry.isLoadingHistory;
export const selectTelemetryCurrentLoading = (state: { telemetry: TelemetryState }) => state.telemetry.isLoadingCurrent;
export const selectTelemetryError = (state: { telemetry: TelemetryState }) => state.telemetry.error;
export const selectTelemetryPagination = (state: { telemetry: TelemetryState }) => state.telemetry.pagination;
export const selectTelemetryFilters = (state: { telemetry: TelemetryState }) => state.telemetry.filters;
export const selectTelemetryConnectionStatus = (state: { telemetry: TelemetryState }) => state.telemetry.isConnected;
export const selectTelemetryLastUpdated = (state: { telemetry: TelemetryState }) => state.telemetry.lastUpdated;

export default telemetrySlice.reducer;
