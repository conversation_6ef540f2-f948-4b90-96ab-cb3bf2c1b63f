import { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import { loginUser, selectIsLoading, selectAuthError } from './authSlice';
import type { LoginCredentials } from '../../types';

const LoginPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  // Get loading and error states from Redux
  const isLoading = useAppSelector(selectIsLoading);
  const error = useAppSelector(selectAuthError);

  const from = location.state?.from?.pathname || '/dashboard';

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const credentials: LoginCredentials = { email, password };

    try {
      const result = await dispatch(loginUser(credentials)).unwrap();
      // Login successful, navigate to the intended page
      navigate(from, { replace: true });
    } catch (error) {
      // Error is already handled by the slice and stored in state
      console.error('Login failed:', error);
    }
  };

  return (
    <div className='card'>
      <form onSubmit={handleSubmit} className='space-y-6'>
        {error && (
          <div className='bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded'>
            {error}
          </div>
        )}

        <div>
          <label
            htmlFor='email'
            className='block text-sm font-medium text-gray-700'
          >
            Email address
          </label>
          <input
            id='email'
            name='email'
            type='email'
            required
            disabled={isLoading}
            className='input-field mt-1 disabled:opacity-50'
            value={email}
            onChange={e => setEmail(e.target.value)}
            placeholder='Enter your email'
          />
        </div>

        <div>
          <label
            htmlFor='password'
            className='block text-sm font-medium text-gray-700'
          >
            Password
          </label>
          <input
            id='password'
            name='password'
            type='password'
            required
            disabled={isLoading}
            className='input-field mt-1 disabled:opacity-50'
            value={password}
            onChange={e => setPassword(e.target.value)}
            placeholder='Enter your password'
          />
        </div>

        <button
          type='submit'
          disabled={isLoading}
          className='btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed'
        >
          {isLoading ? 'Signing in...' : 'Sign in'}
        </button>
      </form>
    </div>
  );
};

export default LoginPage;
